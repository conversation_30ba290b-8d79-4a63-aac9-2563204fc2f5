import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String url = 'https://lpoowdrkshrxmxnsajdk.supabase.co';
  static const String anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imxwb293ZHJrc2hyeG14bnNhamRrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM4NDQzOTksImV4cCI6MjA2OTQyMDM5OX0.eaXsUKboD6Aj-x9xeCgVNOpWhzBpoEv2ZPn_LrPMB-8';
  
  static SupabaseClient get client => Supabase.instance.client;
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: url,
      anonKey: anon<PERSON>ey,
    );
  }
}
