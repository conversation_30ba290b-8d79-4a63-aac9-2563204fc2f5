-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS public.users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    phone VARCHAR(20),
    avatar_url TEXT,
    bio TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);

-- إضا<PERSON><PERSON> Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسة للقراءة (يمكن للجميع قراءة البيانات العامة)
CREATE POLICY "Users can view public profiles" ON public.users
    FOR SELECT USING (true);

-- إنشاء سياسة للتحديث (المستخدم يمكنه تحديث بياناته فقط)
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- إدراج بعض البيانات التجريبية
INSERT INTO public.users (email, full_name, username, phone, bio) VALUES
('<EMAIL>', 'أحمد محمد', 'ahmed_m', '+************', 'مطور تطبيقات موبايل'),
('<EMAIL>', 'سارة أحمد', 'sara_a', '+************', 'مصممة UI/UX'),
('<EMAIL>', 'عمر خالد', 'omar_k', '+************', 'مهندس برمجيات')
ON CONFLICT (email) DO NOTHING;