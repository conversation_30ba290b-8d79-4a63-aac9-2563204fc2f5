# 🎯 تعليمات ربط Flutter + Supabase

## 📋 المعلومات المطلوبة:

1. حزمة Flutter: supabase_flutter: ^2.9.1
2. الباكاج: import 'package:supabase_flutter/supabase_flutter.dart';
3. Project URL: [ضع_الـURL_هنا]
4. API Key: [ضع_الـAPI_Key_هنا]
5. Database Password: [ضع_كلمة_المرور_هنا]

## ✅ ما يجب فعله (فقط الأساسيات):

### 1. إعداد Flutter:
# في pubspec.yaml
dependencies:
  flutter:
    sdk: flutter
  supabase_flutter: ^2.9.1


### 2. إنشاء ملف الإعداد:
// lib/supabase_config.dart
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseConfig {
  static const String url = 'YOUR_PROJECT_URL';
  static const String anonKey = 'YOUR_ANON_KEY';
  
  static SupabaseClient get client => Supabase.instance.client;
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: url,
      anonKey: anon<PERSON>ey,
    );
  }
}


### 3. إعداد Supabase CLI:
# في terminal VS Code
supabase init
supabase link --project-ref YOUR_PROJECT_ID


### 4. إعداد VS Code Tasks:
// .vscode/tasks.json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Supabase: Open Dashboard",
            "type": "shell",
            "command": "start",
            "args": ["https://supabase.com/dashboard/project/YOUR_PROJECT_ID"]
        },
        {
            "label": "Supabase: Create Migration",
            "type": "shell",
            "command": "supabase",
            "args": ["migration", "new", "${input:migrationName}"]
        },
        {
            "label": "Supabase: Push to Database",
            "type": "shell",
            "command": "supabase",
            "args": ["db", "push"]
        },
        {
            "label": "Flutter: Run",
            "type": "shell",
            "command": "flutter",
            "args": ["run"]
        }
    ],
    "inputs": [
        {
            "id": "migrationName",
            "description": "Migration name",
            "default": "new_migration",
            "type": "promptString"
        }
    ]
}


## ❌ ما لا يجب فعله:
- لا تنشئ ملفات .bat كثيرة
- لا تنشئ scripts معقدة
- لا تبدأ البيئة المحلية (supabase start)
- لا تنشئ أي جداول أو بيانات للتجريب
- لا تنشئ صفحات Flutter للاختبار
- فقط الأساسيات للربط والتحكم

## 🎯 النتيجة المطلوبة:
VS Code قادر على:
- إنشاء migrations: Ctrl+Shift+P → Tasks: Run Task → Supabase: Create Migration
- رفع التغييرات: Ctrl+Shift+P → Tasks: Run Task → Supabase: Push to Database
- فتح Dashboard: Ctrl+Shift+P → Tasks: Run Task → Supabase: Open Dashboard
- تشغيل Flutter: Ctrl+Shift+P → Tasks: Run Task → Flutter: Run

## 🔧 اختبار الاتصال:
# للتأكد من الربط
supabase projects list

# للتأكد من حالة المشروع
supabase status --linked


هذا كل شيء - بساطة وفعالية!